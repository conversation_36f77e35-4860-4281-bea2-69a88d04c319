# Google Apps Script Development in VS Code

This project is set up for developing Google Apps Script projects in VS Code with TypeScript support.

## Setup Instructions

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Development Workflow**
   - Edit your code in the `src/` directory
   - Main script file: `src/Code.ts`
   - Apps Script manifest: `src/appsscript.json`

3. **TypeScript Compilation**
   ```bash
   npm run build    # Compile once
   npm run watch    # Watch for changes
   ```

4. **Deploying to Google Apps Script**
   - Copy the compiled JavaScript code from your TypeScript files
   - Paste into the Google Apps Script editor
   - Or use clasp (when available) for automated deployment

## Project Structure
```
gas-project/
├── src/
│   ├── Code.ts           # Main script file
│   └── appsscript.json   # Apps Script manifest
├── .vscode/
│   ├── settings.json     # VS Code settings
│   └── extensions.json   # Recommended extensions
├── tsconfig.json         # TypeScript configuration
├── package.json          # Node.js dependencies
└── README.md            # This file
```

## Features
- TypeScript support with Google Apps Script types
- IntelliSense and autocomplete
- Error checking and linting
- Organized project structure
- VS Code integration

## Next Steps
1. Replace the sample code in `src/Code.ts` with your actual script
2. Install the recommended VS Code extensions
3. Start coding with full TypeScript support!
