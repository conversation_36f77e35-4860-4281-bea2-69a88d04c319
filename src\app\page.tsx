'use client';

import { useEffect } from 'react';
import Image from 'next/image';

export default function Home() {
  useEffect(() => {
    // Create floating particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      if (!particlesContainer) return;

      const particleCount = Math.min(window.innerWidth / 3, 100); // Responsive particle count

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.classList.add('particle');

        // Random position
        const posX = Math.random() * 100;
        const posY = Math.random() * 100;

        // Random size (1-3px)
        const size = Math.random() * 2 + 1;

        // Random opacity
        const opacity = Math.random() * 0.5 + 0.1;

        // Set styles
        particle.style.left = `${posX}%`;
        particle.style.top = `${posY}%`;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.opacity = String(opacity);

        // Add floating animation
        const duration = Math.random() * 20 + 10;
        const delay = Math.random() * 5;

        particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;

        particlesContainer.appendChild(particle);
      }
    }

    // Initialize
    createParticles();
  }, []);

  return (
    <div className="tutum-landing">
      <div className="particles" id="particles"></div>

      <div className="glow glow-1"></div>
      <div className="glow glow-2"></div>

      <div className="container">
        <img src="/tutum-logo.svg" alt="TUTUM" className="logo" />

        <div className="message">
          <h1>An awesome project is being created and will be coming soon</h1>
          <h2>Un projet merveilleux est en cours de création et verra le jour bientôt</h2>
        </div>
      </div>
    </div>
  );
}
