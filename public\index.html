<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TUTUM | Coming Soon</title>
    <style>
        :root {
            --deep-blue: #0a0a2a;
            --light-blue: #1a1a4a;
            --gold: #ffd700;
            --soft-white: #f5f5f5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--deep-blue) 0%, var(--light-blue) 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px;
            overflow: hidden;
            position: relative;
        }

        .container {
            max-width: 800px;
            width: 100%;
            z-index: 2;
        }

        .logo {
            max-width: 80%;
            height: auto;
            margin-bottom: 60px;
            animation: float 6s ease-in-out infinite;
        }

        .message {
            margin-bottom: 40px;
        }

        .message h1 {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            font-weight: 300;
            margin-bottom: 40px;
            line-height: 1.4;
        }

        .message h2 {
            font-size: clamp(1.5rem, 3.5vw, 2.2rem);
            font-weight: 300;
            color: var(--gold);
            line-height: 1.4;
        }

        .glow {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(
                circle,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 215, 0, 0.05) 30%,
                rgba(255, 215, 0, 0.01) 70%,
                rgba(255, 215, 0, 0) 100%
            );
            filter: blur(20px);
            z-index: 1;
            animation: pulse 4s infinite ease-in-out;
        }

        .glow-1 {
            top: 20%;
            left: 15%;
        }

        .glow-2 {
            bottom: 25%;
            right: 10%;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.2); opacity: 0.4; }
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .logo {
                max-width: 90%;
                margin-bottom: 40px;
            }

            .glow {
                width: 200px;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>

    <div class="glow glow-1"></div>
    <div class="glow glow-2"></div>

    <div class="container">
        <img src="TUTUMLOGO.png" alt="TUTUM" class="logo">

        <div class="message">
            <h1>Un projet merveilleux est en cours de création et verra le jour bientôt</h1>
            <h2>An awesome project is being created and will be coming soon</h2>
        </div>
    </div>

    <script>
        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = Math.min(window.innerWidth / 3, 100); // Responsive particle count

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;

                // Random size (1-3px)
                const size = Math.random() * 2 + 1;

                // Random opacity
                const opacity = Math.random() * 0.5 + 0.1;

                // Set styles
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.opacity = opacity;

                // Add floating animation
                const duration = Math.random() * 20 + 10;
                const delay = Math.random() * 5;

                particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;

                particlesContainer.appendChild(particle);
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            createParticles();
        });
    </script>
</body>
</html>
