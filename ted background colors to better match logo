[33mcommit 5521914d5580f137a1fc069d90e167d8d64b0205[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m)[m
Author: <PERSON><PERSON><PERSON> <51678487+<PERSON><PERSON><PERSON>@users.noreply.github.com>
Date:   Thu Apr 24 21:48:32 2025 -0400

    Logo update to png

[1mdiff --git a/public/TUTUMLOGO.png b/public/TUTUMLOGO.png[m
[1mnew file mode 100644[m
[1mindex 0000000..6ceb630[m
Binary files /dev/null and b/public/TUTUMLOGO.png differ
[1mdiff --git a/public/index.html b/public/index.html[m
[1mindex 68527ec..3ae6a93 100644[m
[1m--- a/public/index.html[m
[1m+++ b/public/index.html[m
[36m@@ -11,13 +11,13 @@[m
             --gold: #ffd700;[m
             --soft-white: #f5f5f5;[m
         }[m
[31m-        [m
[32m+[m
         * {[m
             margin: 0;[m
             padding: 0;[m
             box-sizing: border-box;[m
         }[m
[31m-        [m
[32m+[m
         body {[m
             font-family: 'Arial', sans-serif;[m
             min-height: 100vh;[m
[36m@@ -32,38 +32,38 @@[m
             overflow: hidden;[m
             position: relative;[m
         }[m
[31m-        [m
[32m+[m
         .container {[m
             max-width: 800px;[m
             width: 100%;[m
             z-index: 2;[m
         }[m
[31m-        [m
[32m+[m
         .logo {[m
             max-width: 80%;[m
             height: auto;[m
             margin-bottom: 60px;[m
             animation: float 6s ease-in-out infinite;[m
         }[m
[31m-        [m
[32m+[m
         .message {[m
             margin-bottom: 40px;[m
         }[m
[31m-        [m
[32m+[m
         .message h1 {[m
             font-size: clamp(1.8rem, 4vw, 2.5rem);[m
             font-weight: 300;[m
             margin-bottom: 40px;[m
             line-height: 1.4;[m
         }[m
[31m-        [m
[32m+[m
         .message h2 {[m
             font-size: clamp(1.5rem, 3.5vw, 2.2rem);[m
             font-weight: 300;[m
             color: var(--gold);[m
             line-height: 1.4;[m
         }[m
[31m-        [m
[32m+[m
         .glow {[m
             position: absolute;[m
             width: 300px;[m
[36m@@ -80,17 +80,17 @@[m
             z-index: 1;[m
             animation: pulse 4s infinite ease-in-out;[m
         }[m
[31m-        [m
[32m+[m
         .glow-1 {[m
             top: 20%;[m
             left: 15%;[m
         }[m
[31m-        [m
[32m+[m
         .glow-2 {[m
             bottom: 25%;[m
             right: 10%;[m
         }[m
[31m-        [m
[32m+[m
         .particles {[m
             position: absolute;[m
             top: 0;[m
[36m@@ -100,7 +100,7 @@[m
             overflow: hidden;[m
             z-index: 0;[m
         }[m
[31m-        [m
[32m+[m
         .particle {[m
             position: absolute;[m
             width: 2px;[m
[36m@@ -108,25 +108,25 @@[m
             background-color: rgba(255, 255, 255, 0.3);[m
             border-radius: 50%;[m
         }[m
[31m-        [m
[32m+[m
         @keyframes float {[m
             0% { transform: translateY(0px); }[m
             50% { transform: translateY(-20px); }[m
             100% { transform: translateY(0px); }[m
         }[m
[31m-        [m
[32m+[m
         @keyframes pulse {[m
             0%, 100% { transform: scale(1); opacity: 0.3; }[m
             50% { transform: scale(1.2); opacity: 0.4; }[m
         }[m
[31m-        [m
[32m+[m
         /* Mobile responsiveness */[m
         @media (max-width: 768px) {[m
             .logo {[m
                 max-width: 90%;[m
                 margin-bottom: 40px;[m
             }[m
[31m-            [m
[32m+[m
             .glow {[m
                 width: 200px;[m
                 height: 200px;[m
[36m@@ -136,56 +136,56 @@[m
 </head>[m
 <body>[m
     <div class="particles" id="particles"></div>[m
[31m-    [m
[32m+[m
     <div class="glow glow-1"></div>[m
     <div class="glow glow-2"></div>[m
[31m-    [m
[32m+[m
     <div class="container">[m
[31m-        <img src="tutum-logo.svg" alt="TUTUM" class="logo">[m
[31m-        [m
[32m+[m[32m        <img src="TUTUMLOGO.png" alt="TUTUM" class="logo">[m
[32m+[m
         <div class="message">[m
             <h1>An awesome project is being created and will be coming soon</h1>[m
             <h2>Un projet merveilleux est en cours de création et verra le jour bientôt</h2>[m
         </div>[m
     </div>[m
[31m-    [m
[32m+[m
     <script>[m
         // Create floating particles[m
         function createParticles() {[m
             const particlesContainer = document.getElementById('particles');[m
             const particleCount = Math.min(window.innerWidth / 3, 100); // Responsive particle count[m
[31m-            [m
[32m+[m
             for (let i = 0; i < particleCount; i++) {[m
                 const particle = document.createElement('div');[m
                 particle.classList.add('particle');[m
[31m-                [m
[32m+[m
                 // Random position[m
                 const posX = Math.random() * 100;[m
                 const posY = Math.random() * 100;[m
[31m-                [m
[32m+[m
                 // Random size (1-3px)[m
                 const size = Math.random() * 2 + 1;[m
[31m-                [m
[32m+[m
                 // Random opacity[m
                 const opacity = Math.random() * 0.5 + 0.1;[m
[31m-                [m
[32m+[m
                 // Set styles[m
                 particle.style.left = `${posX}%`;[m
                 particle.style.top = `${posY}%`;[m
                 particle.style.width = `${size}px`;[m
                 particle.style.height = `${size}px`;[m
                 particle.style.opacity = opacity;[m
[31m-                [m
[32m+[m
                 // Add floating animation[m
                 const duration = Math.random() * 20 + 10;[m
                 const delay = Math.random() * 5;[m
[31m-                [m
[32m+[m
                 particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;[m
[31m-                [m
[32m+[m
                 particlesContainer.appendChild(particle);[m
             }[m
         }[m
[31m-        [m
[32m+[m
         // Initialize[m
         window.addEventListener('load', () => {[m
             createParticles();[m
