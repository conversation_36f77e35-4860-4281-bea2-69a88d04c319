@import "tailwindcss";

:root {
  --deep-blue: #0a0a2a;
  --light-blue: #1a1a4a;
  --gold: #ffd700;
  --soft-white: #f5f5f5;
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 0;
}

.tutum-landing {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--deep-blue) 0%, var(--light-blue) 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  overflow: hidden;
  position: relative;
}

.container {
  max-width: 800px;
  width: 100%;
  z-index: 2;
}

.logo {
  max-width: 80%;
  height: auto;
  margin-bottom: 60px;
  animation: float 6s ease-in-out infinite;
}

.message {
  margin-bottom: 40px;
}

.message h1 {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 300;
  margin-bottom: 40px;
  line-height: 1.4;
}

.message h2 {
  font-size: clamp(1.5rem, 3.5vw, 2.2rem);
  font-weight: 300;
  color: var(--gold);
  line-height: 1.4;
}

.glow {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 215, 0, 0.05) 30%,
      rgba(255, 215, 0, 0.01) 70%,
      rgba(255, 215, 0, 0) 100%
  );
  filter: blur(20px);
  z-index: 1;
  animation: pulse 4s infinite ease-in-out;
}

.glow-1 {
  top: 20%;
  left: 15%;
}

.glow-2 {
  bottom: 25%;
  right: 10%;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.4; }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .logo {
      max-width: 90%;
      margin-bottom: 40px;
  }

  .glow {
      width: 200px;
      height: 200px;
  }
}
