/**
 * Google Apps Script main file
 * This is where you'll place your script code
 */

function onOpen() {
  // This function runs when the spreadsheet is opened
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Custom Menu')
    .addItem('Run Function', 'myFunction')
    .addToUi();
}

function myFunction() {
  // Sample function - replace with your actual code
  const sheet = SpreadsheetApp.getActiveSheet();
  const range = sheet.getRange('A1');
  range.setValue('Hello from Apps Script!');
  
  Logger.log('Function executed successfully');
}

// Add your custom functions here
// You can replace this entire file with your actual script
