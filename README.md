# TUTUM Project

This is the TUTUM project landing page. The project is currently in development.

## Deployment

This project is configured to be deployed on Vercel. The main branch will be automatically deployed.

### Steps to deploy:

1. Push the code to the main branch of the GitHub repository
2. Vercel will automatically detect changes and deploy the site

## Development

### Static Landing Page

The static landing page is located in the `public` directory. It can be viewed locally by opening the `public/index.html` file in a browser.

### Next.js Application (Future Development)

The Next.js application is set up but currently not used for the landing page. It will be used for future development with Firebase SDK integration.

To run the Next.js development server (once dependencies are properly installed):

```bash
npm run dev
```

## Firebase Integration

Firebase SDK integration is planned for future development. The configuration files are already set up in the `src/firebase` directory.
